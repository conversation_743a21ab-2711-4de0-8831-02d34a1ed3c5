// pollManager.js
import { ref, onBeforeUnmount } from 'vue'
import axios from 'axios'

export const usePolling = (options = {}) => {
  // 合并默认配置
  const config = {
    url: '/api/data',
    interval: 5000,
    timeout: 3000,
    maxRetries: 3,
    autoStart: true,
    ...options
  }

  // 响应式状态
  const isPolling = ref(false)
  const data = ref(null)
  const errorCount = ref(0)
  const lastPollTime = ref(null)

  // 控制变量
  let pollTimer = null
  let abortController = null

  // 核心轮询方法
  const executePoll = async () => {
    if (!isPolling.value) return

    try {
      abortController = new AbortController()
      
      const response = await axios.get(config.url, {
        timeout: config.timeout,
        signal: abortController.signal
      })

      data.value = response.data
      errorCount.value = 0
      lastPollTime.value = new Date()
      options.onSuccess?.(response.data) // 成功回调
      scheduleNextPoll()
    } catch (error) {
      if (axios.isCancel(error)) return
      handlePollError(error)
    } finally {
      abortController = null
    }
  }

  // 智能调度
  const scheduleNextPoll = () => {
    if (isPolling.value) {
      clearTimeout(pollTimer)
      pollTimer = setTimeout(executePoll, config.interval)
    }
  }

  // 错误处理
  const handlePollError = (error) => {
    errorCount.value++
    options.onError?.(error) // 错误回调

    if (errorCount.value >= config.maxRetries) {
      stop()
      return
    }

    const backoffDelay = Math.min(1000 * 2 ** errorCount.value, 30000)
    pollTimer = setTimeout(executePoll, backoffDelay)
  }

  // 公共方法
  const start = () => {
    if (!isPolling.value) {
      isPolling.value = true
      executePoll()
    }
  }

  const stop = () => {
    isPolling.value = false
    clearTimeout(pollTimer)
    if (abortController) {
      abortController.abort()
    }
  }

  // 生命周期
  onBeforeUnmount(stop)

  // 网络状态监听
  const handleNetworkChange = () => {
    if (navigator.onLine && !isPolling.value) {
      start()
    } else {
      stop()
    }
  }

  if (config.autoStart) start()
  window.addEventListener('online', handleNetworkChange)
  window.addEventListener('offline', stop)

  // 返回可调用接口
  return {
    data,
    isPolling,
    errorCount,
    lastPollTime,
    start,
    stop
  }
}
