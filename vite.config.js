import { fileURLToPath, URL } from 'node:url'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import Components from 'unplugin-vue-components/vite'
import { VantResolver } from 'unplugin-vue-components/resolvers'
import { loadEnv } from "vite";
console.log("process", process.cwd())
// https://vitejs.dev/config/
export default ((mode) => {

    return defineConfig({
        root: "./",
        publicDir: "./public",
        envDir: "./",
        base: loadEnv(mode.mode, process.cwd()).VITE_APP_FILE || "./",
        // base: "./",
        server: {
            port: 8081,
            proxy: {
                '/health_mall': {
                    // target: '//backend-api-01.newbee.ltd/api/v1', // 凡是遇到 /api 路径的请求，都映射到 target 属性
                    target: 'https://jsbceshi.hfi-health.com:18188/health_mall', // 凡是遇到 /api 路径的请求，都映射到 target 属性
                    changeOrigin: true,
                    rewrite: path => path.replace(/^\/health_mall/, '')
                    // pathRewrite: {
                    //     "^/health_mall": "",
                    // },
                }
            },
            host:'0.0.0.0',  
        },
        plugins: [
            vue(),
            Components({ resolvers: [VantResolver()] })
        ],
        build: {
            target: 'es2015',
        },
        resolve: {
            alias: {
                '@': fileURLToPath(new URL('./src', import.meta.url))
            }
        }
    })
})

