<!--
 * 严肃声明：
 * 开源版本请务必保留此注释头信息，若删除我方将保留所有法律责任追究！
 * 本系统已申请软件著作权，受国家版权局知识产权以及国家计算机软件著作权保护！
 * 可正常分享和学习源码，不得用于违法犯罪活动，违者必究！
 * Copyright (c) 2020 陈尼克 all rights reserved.
 * 版权所有，侵权必究！
 *
-->

<template>
  <div class="order-detail-box">
    <!-- <s-header :name="'订单详情'" @callback="close"></s-header> -->
    <!-- <div class="order-status">
      <div class="status-item">
        <label>订单状态：</label>
        <span>{{ state.detail.orderStatusString }}</span>
      </div>
      <div class="status-item">
        <label>订单编号：</label>
        <span>{{ state.detail.orderNo }}</span>
      </div>
      <div class="status-item">
        <label>下单时间：</label>
        <span>{{ state.detail.createTime }}</span>
      </div>
      <van-button v-if="state.detail.orderStatus == 3" style="margin-bottom: 10px" color="#1baeae" block @click="handleConfirmOrder(state.detail.orderNo)">确认收货</van-button>
      <van-button v-if="state.detail.orderStatus == 0" style="margin-bottom: 10px" color="#1baeae" block @click="showPayFn">去支付</van-button>
      <van-button v-if="!(state.detail.orderStatus < 0 || state.detail.orderStatus == 4)" block @click="handleCancelOrder(state.detail.orderNo)">取消订单</van-button>
    </div> -->
    <!-- <div class="order-price">
      <div class="price-item">
        <label>商品金额：</label>
        <span>¥ {{ state.detail.totalPrice }}</span>
      </div>
      <div class="price-item">
        <label>配送方式：</label>
        <span>普通快递</span>
      </div>
    </div> -->
    <div class="content">
     
    <div class="custom-box">
    <van-card
      v-for="item in state.detail.orderItemVOs"
      :key="item.goodsId"
      style="background: #fff;"
      :num="item.goodsCount"
      
      :desc="state.detail.goodsSpecification"
      :title="item.goodsName"
      :thumb="$filters.prefix(item.goodsCoverImg)"
      >

      <template #price>
      <p style="color: #333333;font-size: 12px;">¥ <span style='font-size:16px;color: #333;'>{{ String(formatPrice(item.sellingPrice)).split('.')[0] }}</span><span style='font-size:12px;color: #333;'></span><span>{{String(formatPrice(item.sellingPrice)).split('.')[1] }}</span></p>
    </template>

      <template #tags>
        
        <div
        :style="{ color: getColor(state.detail.orderStatus) }"
        class="corner-tag">{{state.detail.orderStatus===0?'待支付':state.detail.orderStatus===1?'待发货':state.detail.orderStatus===2||state.detail.orderStatus===3||state.detail.orderStatus===6?'待使用':state.detail.orderStatus===4||state.detail.orderStatus===5?'已收货':(state.detail.orderStatus===-3&&state.detail.payStatus===3)?'已关闭':'已取消'}}</div>
      </template>
            </van-card>

            <!-- <van-cell 

 
:value="'应付:¥' + state.detail.totalPrice"



value-class="custom-value" 
:border="false"
/> -->

<!-- 实线 -->
<van-divider :style="{ width: '90%', margin: '0 auto'}" />

<van-cell 
  v-if="state.detail.orderStatus!== -3"
  title="商品总额"
  
  title-style="color: #666; font-size: 14px;"
  value-class="custom-value" 
  :border="false"
  
>
<p style="color: #333333;font-size: 12px;">¥ <span style='font-size:16px;color: #333;'>{{ String(state.detail.totalPrice).split('.')[0] }}</span><span style='font-size:12px;color: #333;'></span><span>{{String(state.detail.totalPrice).split('.')[1] }}</span></p>

</van-cell>

<van-cell 
v-if="state.detail.orderStatus!== -3"
  title="实付"
  
  title-style="color: #666; font-size: 14px;"
  value-class="custom-value" 
  :border="false"
>
<p style="color: #333333;font-size: 12px;">¥ <span style='font-size:16px;color: #333;'>{{ String(state.detail.totalPrice).split('.')[0] }}</span><span style='font-size:12px;color: #333;'></span><span>{{String(state.detail.totalPrice).split('.')[1] }}</span></p>

</van-cell>

<van-cell 
v-if="state.detail.orderStatus=== -3"

  title="商品实付"
 
  title-style="color: #666; font-size: 14px;"
  value-class="custom-value" 
  :border="false"
>
<p style="color: #333333;font-size: 12px;">¥ <span style='font-size:16px;color: #333;'>{{ String(state.detail.totalPrice).split('.')[0] }}</span><span style='font-size:12px;color: #333;'></span><span>{{String(state.detail.totalPrice).split('.')[1] }}</span></p>

</van-cell>

<van-cell 
v-if="state.detail.orderStatus=== -3"

  title="申请金额"
  
  title-style="color: #666; font-size: 14px;"
  value-class="custom-value" 
  :border="false"
>
<p style="color: #333333;font-size: 12px;">¥ <span style='font-size:16px;color: #333;'>{{ String(state.detail.totalPrice).split('.')[0] }}</span><span style='font-size:12px;color: #333;'></span><span>{{String(state.detail.totalPrice).split('.')[1] }}</span></p>

</van-cell>

</div>

<div    v-if="state.detail.orderStatus=== -3"
class="custom-box" style="margin-top: 10px;">
<van-cell 
 :border="false"
  title="申请结果"
  value="商家同意"
  title-style="color: #666; font-size: 14px;"
  value-class="custom-value" 
/>
<van-cell 
 :border="false"
  title="交易结果"
  value="退款成功"
  title-style="color: #666; font-size: 14px;"
  value-class="custom-value" 
/>
<van-cell 
 :border="false"
  title="退款方式"
  value="原支付返还"
  title-style="color: #666; font-size: 14px;"
  value-class="custom-value" 
/>

</div>


<div v-if="state.detail.orderStatus===2||state.detail.orderStatus===3||state.detail.orderStatus===6" class="custom-box" style="margin-top: 10px;">
  <div class="qr-code-wrapper-x" >
    <div  ref="qrCanvas"></div>
  <p style="font-size: 14px;" v-if="state.detail?.orderItemVOs?.length > 0">已核销 <span style="color: #01CDA7">{{state.detail.orderItemVOs[0].writeOffCount}}</span> 份,剩余 <span style="color: #01CDA7">{{state.detail.orderItemVOs[0].goodsCount-state.detail.orderItemVOs[0].writeOffCount}}</span> 份</p>
  <!-- <p style="cursor: pointer;" v-if="state.detail?.orderItemVOs?.length > 0&&state.detail.orderItemVOs[0].refundCount===0"  @click="goTo(state.detail.orderNo)">查看退款详情</p> -->
  <van-button  v-if="state.detail?.orderItemVOs?.length > 0&&state.detail.orderItemVOs[0].refundCount > 0"  @click="goTo(state.detail.orderNo)" round type="default" class="custom-btn-refund" >查看退款详情</van-button>  

</div>
  

</div>

<div class="custom-box" style="margin-top: 10px;">
<van-cell 


  title="订单编号"
 :value="state.detail.orderNo"
  title-style="color: #666; font-size: 14px;"
  value-class="custom-value" 

  :border="false"
/>

<van-cell 
v-if="state.detail.orderStatus!== -3"

  title="创建时间"
  :value="state.detail.createTime"
  title-style="color: #666; font-size: 14px;"
  value-class="custom-value" 
  :border="false"
/>
<van-cell 
v-if="state.detail.orderStatus=== 2||state.detail.orderStatus=== 3||state.detail.orderStatus=== 6||state.detail.orderStatus=== 4||state.detail.orderStatus=== 5"

  title="支付方式"
  :value="state.detail.payTypeString"
  title-style="color: #666; font-size: 14px;"
  value-class="custom-value" 
  :border="false"
/>
<van-cell 
v-if="state.detail.orderStatus=== 2||state.detail.orderStatus=== 3||state.detail.orderStatus=== 6||state.detail.orderStatus=== 4||state.detail.orderStatus=== 5"
  title="支付流水号"
 
  :title-style="{ 'max-width': '80px','color': '#666','font-size': '14px' }" 
  
  :border="false"
>
<!-- <p ><span style='width: 100%;'>{{ state.detail.payOrderNo }}</span></p> -->
<p ><span style=' white-space: nowrap; '>{{ state.detail.payOrderNo }}</span></p>
</van-cell>
<van-cell 
 v-if="state.detail.orderStatus=== 2||state.detail.orderStatus=== 3||state.detail.orderStatus=== 6||state.detail.orderStatus=== 4||state.detail.orderStatus=== 5"
  title="下单时间"
   :value="state.detail.payTime"
  title-style="color: #666; font-size: 14px;"
  value-class="custom-value" 
  :border="false"
/>
<van-cell 
v-if="state.detail.orderStatus=== -3"

  title="退款编号"
   :value="state.detail.refundSeq"
  title-style="color: #666; font-size: 14px;"
  value-class="custom-value" 
  :border="false"
/>
<van-cell 
v-if="state.detail.orderStatus=== -3"

  title="申请时间"
   :value="state.detail.refundTime"
  title-style="color: #666; font-size: 14px;"
  value-class="custom-value" 
  :border="false"
/>

</div>

 <!-- 底部固定双按钮容器 -->  
 <div v-if="state.detail.orderStatus == 0||state.detail.orderStatus == 2||state.detail.orderStatus == 3||state.detail.orderStatus == 6" class="fixed-footer" >  
  <van-button v-if="state.detail.orderStatus == 0" round type="default" class="custom-btn-cancel"  @click="handleCancelOrder(state.detail.orderNo)">取消订单</van-button>  
    <van-button v-if="state.detail.orderStatus == 2||state.detail.orderStatus == 3||state.detail.orderStatus == 6" round type="default" class="custom-btn-cancel"  @click="handleCancelOrder(state.detail.orderNo)">申请退款</van-button>  
    <van-button v-if="state.detail.orderStatus == 0" round type="primary" class="custom-btn" @click="handleSubmit(state.detail)">去支付</van-button>  
    <!-- <van-button v-if="state.detail.orderStatus == 2||state.detail.orderStatus == 3||state.detail.orderStatus == 6" type="primary" class="custom-btn-qr" @click="showQRPopup(state.detail.orderNo)" >查看核销二维码</van-button>   -->

  </div>  
</div>
<!-- 二维码弹框 -->
<!-- <van-popup
      v-model:show="isPopupVisible"
      position="center"
      round
      :style="{ width: '80%', padding: '20px' }"
      @click-overlay="closeQRPopup"
    >
      <div class="qr-code-wrapper">
        <canvas ref="qrCanvas"></canvas>
        <p class="tip">请将核销二维码出示给工作人员</p>
        <van-button plain  @click="closeQRPopup">关闭</van-button>
      </div>
    </van-popup> -->

    <van-popup
  v-model:show="showPopup"
  round
  position="center"
  :style="{ width: '80%', borderRadius: '12px' }"
>
  <!-- 标题 -->
  <!-- <h3 class="popup-title">操作确认</h3> -->

  <!-- 内容区域 (整体居中) -->
  <div class="popup-content">
    <p v-if="state.detail.orderStatus == 0" style="margin-bottom: 20px;">是否取消该订单?</p>

    <p v-if="state.detail.orderStatus == 2||state.detail.orderStatus == 3||state.detail.orderStatus == 6" style="margin-bottom: 20px;">该订单剩余{{state.detail.orderItemVOs[0].goodsCount-state.detail.orderItemVOs[0].writeOffCount}}份,是否<br>申请退款?</p>

    <!-- 居中带边框按钮 -->
    <van-button
      class="center-btn"
      type="default"
      @click="handleConfirm()"
    >
      是
    </van-button>
 

  <!-- 底部独立按钮 -->
  <van-button
    class="bottom-btn"
    block
    plain
    type="primary"
    @click="showPopup = false"
  >
    否
  </van-button>
</div>
</van-popup>
<!-- 二维码弹框 -->
<van-popup
      v-model:show="isPopupVisible"
      position="center"
      round
      :style="{ width: '80%', padding: '20px' }"
      @click-overlay="closeQRPopup"
    >
      <div class="qr-code-wrapper" >
        <div style="margin-top: 20px;" ref="qrCanvass"></div>
        <p class="tip">请将核销二维码出示给工作人员</p>
        <van-button plain style=" border: none; outline: none;   "  @click="closeQRPopup">关闭</van-button>
      </div>
    </van-popup>
    <!-- <van-popup
      v-model:show="state.showPay"
      position="bottom"
      :style="{ height: '24%' }"
    >
      <div :style="{ width: '90%', margin: '0 auto', padding: '20px 0' }">
        <van-button :style="{ marginBottom: '10px' }" color="#1989fa" block @click="handlePayOrder(state.detail.orderNo, 1)">支付宝支付</van-button>
        <van-button color="#4fc08d" block @click="handlePayOrder(state.detail.orderNo, 2)">微信支付</van-button>
      </div>
    </van-popup> -->
  </div>
</template>

<script setup>
import { reactive, ref, onMounted } from 'vue'
import sHeader from '@/components/SimpleHeader.vue'
import { getOrderDetail, cancelOrder, confirmOrder, payOrder,createOrder } from '@/service/order'
import { usePolling } from '../service/pollManager'
import { showConfirmDialog, showLoadingToast, closeToast, showSuccessToast, closeDialog } from 'vant'
import { useRouter,useRoute } from 'vue-router'
import QRCode from 'qrcodejs2-fix'
import { addCart } from '@/service/cart'  // 添加这行
const route = useRoute()
const router = useRouter()
const state = reactive({
  detail: {},
  showPay: false,
  orderId:''
})

onMounted(() => {
  init()
})

const formatPrice = (price) => {
  return Number.isInteger(price) ? 
    price :               // 整数直接显示（如：100）
    price.toFixed(2)      // 小数保留两位（如：99.90）
}

const goTo = (id) => {
  // console.log('1111111111')
  router.push({ path: `/order-detail-refund/${id}`})
  // router.replace({ path: `/order-detail/${id}`}).then(() => {
  //   router.replace({ path: `/order-detail/${id}` });
  // });
  // const { data } =  getOrderDetail(id)
  // state.detail = data
  // closeToast()

  // const newQuery = { 
  //   ...router.currentRoute.value.query,
  //   id: id
  // };
  // const fullPath = router.resolve({
  //   path: '/order-detail',
  //   query: newQuery
  // }).href;
  // window.open(fullPath, '_blank');

}




const getColor = (score) => {

// item.orderStatus===0?'待支付':item.orderStatus===1?'待发货':item.orderStatus===2||item.orderStatus===3||item.orderStatus===6?'待使用':item.orderStatus===4||item.orderStatus===5||item.orderStatus===-1||item.orderStatus===-2?'已收货':item.orderStatus===-3?'已取消':'未知状态'

  if (score ===0) return '#FA541C' // 优秀绿色
  if (score ===1) return '#FF9500' // 及格橙色
  if (score ===2||score ===3||score ===6) return '#FF9500' // 及格橙色
  if (score ===4||score ===5||score ===-1||score ===-2||score ===-3) return '#999999' // 及格橙色
  return '#999999' // 不及格红色
}

const init = async () => {
  showLoadingToast({
    message: '加载中...',
    forbidClick: true
  });
  const { id } = route.params
  const { data } = await getOrderDetail(id)
  state.detail = data
  closeToast()

  if(state.detail.orderStatus===2||state.detail.orderStatus===3||state.detail.orderStatus===6){
 // 确保 DOM 更新后生成二维码
 setTimeout(() => {
        generateQRCode(state.detail.orderNo); // 替换为你的链接或文本
      }, 50);

  }
  
}

const isPopupVisible = ref(false);
const showPopup = ref(false);
const qrCanvas = ref(null);
let qrcodeInstance = null;

    // 生成二维码
    const generateQRCode = async (text) => {
      try {
        // console.log(this.qrcode,'qrcode')
        // if(this.qrcode !=null){
        //   this.qrcode.clear();
        // }
      
        if (qrcodeInstance) {
          qrCanvas.value.innerHTML = ''; // 清空容器内容 ‌:ml-citation{ref="1,4" data="citationList"}
    qrcodeInstance = null;
  }
  qrcodeInstance  =  new QRCode(qrCanvas.value,{
      text:text,
         width: 150,
       height: 150,
       
       colorDark: "#000000",
       colorLight: "#ffffff",
       correctLevel: QRCode.CorrectLevel.H
       });
        // qrcode.clear();
      //   new QRCode(qrCanvas.value, {
      //   text: "https://www.baidu.com/", // 需要转换为二维码的内容
      //   width: 100,
      //   height: 100,
      //   colorDark: "#000000",
      //   colorLight: "#ffffff",
      //   correctLevel: QRCode.CorrectLevel.H
      // });


      } catch (error) {
        console.error('生成二维码失败:', error);
      }
    }
    // 打开弹框
    const showQRPopup  = (orderNo) => {
      isPopupVisible.value = true;
      // 确保 DOM 更新后生成二维码
      setTimeout(() => {
        generateQRCode(orderNo); // 替换为你的链接或文本
      }, 50);
      // usePollingx
    }


  // 关闭弹框
  const usePollingx  = () => {
          // 初始化轮询实例
          const { 
  data, 
  isPolling, 
  errorCount,
  start,
  stop
} = usePolling({
  url: '/api/real-time-data',
  interval: 8000,
  onSuccess: (data) => {
    console.log('最新数据:', data)
    isPopupVisible.value = false;
    state.status = 4;
    router.push({ path: '/order', query: { id } })
  },
  onError: (err) => {
    console.error('轮询错误:', err)
  }
})
    }



    // 关闭弹框
    const closeQRPopup  = () => {
      isPopupVisible.value = false;
      // router.push({ path: '/order/3'})
    }

    const handleConfirm  = () => {
      // showPopup.value = true;
      cancelOrder(state.orderId).then(res => {
        // console.log('111111')
      if (res.resultCode === 200) {
        showSuccessToast('取消成功')
        showPopup.value = false;
        init();
        // router.go(-1)
        // router.push({ path: '/order/5'})
        // init()
        // state.status = '3'
        // onRefresh()
      //   setTimeout(() => {
      //     state.status = 2
      // }, 1000);
        
        
       
      }else{
        showFailToast('取消失败')
        showPopup.value = false;
      }
    })
    };

const handleCancelOrder = (id) => {
  showPopup.value = true;
  state.orderId = id;
  // showConfirmDialog({
  //   title: '确认取消订单？',
  // }).then(() => {
  //   cancelOrder(id).then(res => {
  //     if (res.resultCode == 200) {
  //       showSuccessToast('删除成功')
  //       init()
  //     }
  //   })
  // }).catch(() => {
  //   // on cancel
  // });
}

const handleConfirmOrder = (id) => {
  showConfirmDialog({
    title: '是否确认订单？',
  }).then(() => {
    confirmOrder(id).then(res => {
      if (res.resultCode == 200) {
        showSuccessToast('确认成功')
        init()
      }
    })
  }).catch(() => {
    // on cancel
  });
}

const showPayFn = () => {
  state.showPay = true
}

const handlePayOrder = async (id, type) => {
  await payOrder({ orderNo: id, payType: type })
  state.showPay = false
  init()
}

const close = () => {
  closeDialog
}

const handleSubmit = async (item) => {
 try {
// 先加入购物车
//  const { data: cartData, resultCode } = await addCart({ 
//  goodsCount: item.orderItemVOs[0].goodsCount, 
//  goodsId: item.orderItemVOs[0].goodsId 
//  })

//  if(resultCode !== 200) {
// showToast('加入购物车失败')
// return
// }
//  console.log("handleSubmit",cartData)

 // 构造支付参数
 const wxAppid = window.localStorage.getItem('wxAppid')
 const wxOpenid = window.localStorage.getItem('wxOpenid')
const alipayUserId = window.localStorage.getItem('alipayUserId')
        // 根据环境构造remark2
        let remark2 = "";
        if (
            navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1 &&
            wxAppid &&
            wxOpenid
        ) {
            // 微信环境
            remark2 = JSON.stringify({
                openid: wxOpenid,
                appid: wxAppid,
            });
        } else if (navigator.userAgent.indexOf("AliApp") > -1 && alipayUserId) {
            //支付宝
            // showToast("未获取到用户信息");
            // 支付宝环境
            remark2 = JSON.stringify({
                openid: alipayUserId,
            });
        } else {
            showToast("未获取到用户wxAppid和wxOpenid");
            return;
        }

        console.log("remark2remark2remark2remark2", remark2);


 const baseUrl = `${window.location.protocol}//${window.location.host}${window.location.pathname.split('/')[0]}/#/order-detail/${item.orderNo}`
 // 创建订单
const params = {
  orderNo: item.orderNo,
callBackUrl: baseUrl,
 remark2
}

const res = await payOrder(params)
 console.log("handleSubmit2",res) 
 if (res.resultCode === 200 && res.data?.cashierUrl) {
// 跳转到收银台
window.location.href = res.data.cashierUrl
 } else {
 showToast('拉起收银台失败')
 }
 } catch (error) {
 showToast('下单失败')
}
}

</script>

<style lang="less" scoped>
  .order-detail-box {
    background: #f7f7f7;
    height:100vh;

    .qr-code-wrapper {
      text-align: center;
          height: 300px;        /* 必须定义高度 */  
          display: grid;  
          place-items: center;  /* 同时水平和垂直居中 */
          
        }
        .qr-code-wrapper-x {
      text-align: center;
          height: 250px;        /* 必须定义高度 */  
          display: grid;  
          place-items: center;  /* 同时水平和垂直居中 */
          
        }
        .tip {
          margin-top: 50px;
          color: #666;
          border: none;      /* 移除边框 */
          outline: none;     /* 移除聚焦时的轮廓线 */
        }
    .content {
      height: calc(~"(100vh - 70px)");
      overflow: hidden;
      overflow-y: scroll; 
      background: #f7f7f7;
      margin: 0px 10px;
      
    }
    .order-status {
      background: #fff;
      padding: 20px;
      font-size: 15px;
      .status-item {
        margin-bottom: 10px;
        label {
          color: #999;
        }
        span {

        }
      }
    }
    .order-price {
      background: #fff;
      margin: 20px 0;
      padding: 20px;
      font-size: 15px;
      .price-item {
        margin-bottom: 10px;
        label {
          color: #999;
        }
        span {

        }
      }
    }
    .van-card {
      margin-top: 0;
    }
    .van-card__content {
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
    .custom-box {
      display: flex;
  flex-direction: column; /* 垂直排列 */
  // align-items: center;    /* 水平居中 */
  justify-content: center; /* 垂直居中 */
  gap: 5px;
  // min-height: 100px;       /* 最小高度 */
  border-radius: 8px;      /* 圆角 */
  padding-top: 10px;
  padding-bottom: 5px;
  margin-top: 10px;
  background: #ffffff;      /* 最小高度确保容器可见 */
}

/* 弹窗基础样式 */
.van-popup--center {
  background: #fff !important;
  // padding-bottom: 50px; /* 给底部按钮留空间 */
}

/* 标题样式 */
.popup-title {
  padding: 16px;
  margin: 0;
  font-size: 18px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0; /* 标题下边框 */
}

/* 内容区域 */
.popup-content {
  padding: 20px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 居中带边框按钮 */
.center-btn {
  
  width: 60%;
  border: 1px solid #01CDA7 !important;
  color: #01CDA7 !important;
  border-radius: 20px;
}

/* 底部按钮 */
.bottom-btn {
  margin-top: 10px;
  color: #333333 !important;
  // border-top: 1px solid #f0f0f0; /* 按钮上边框 */
  border-radius: 0 0 12px 12px; /* 匹配弹窗圆角 */
  border: none;      /* 移除边框 */
  outline: none;     /* 移除聚焦时的轮廓线 */
}

/* 修改所有分割线颜色 */
.van-divider--hairline::after {
  border-color: #1890ff !important;
}

/* 修改右侧文字样式 */
:deep(.van-cell__value) {
  color: #333;
  font-weight: bold;
}
:deep(.van-cell) {
  height: 35px !important;    /* 固定高度 */
  padding: 10px 15px 0 15px;         /* 调整内间距 */
  line-height: 15px;           /* 文本行高 */
}


      :deep(.van-card__title) {
  // color: rgb(119, 0, 255); /* 红色 */
  // font-size: 16px; /* 字体大小 */
        width: 65%;
}
:deep(.van-card__desc) {
  // color: rgb(119, 0, 255); /* 红色 */
  // font-size: 16px; /* 字体大小 */
        // width: 65%;
       margin-top: 40px;

}
:deep(.van-card__price) {
  // color: rgb(119, 0, 255); /* 红色 */
  // font-size: 16px; /* 字体大小 */
        // width: 65%;
        display: flex !important;
        font-size: 10px;
        margin-top: -70px;
          justify-content: flex-end !important; 

}
:deep(.van-card__num) {
  // color: rgb(119, 0, 255); /* 红色 */
  // font-size: 16px; /* 字体大小 */
        // width: 65%;
       margin-top: 25px;

}
// :deep(.van-cell) {
//   height: 20px !important;    /* 固定高度 */
//   padding: 1px 10px;         /* 调整内间距 */
//   line-height: 15px;           /* 文本行高 */
  
// }
// :deep(.van-cell__value > span) {
//   color: #333 !important;
// }

.corner-tag {
  position: absolute;
  top: -3px;
  right: -5px;
  // bottom: 5px;
  padding: 1px 1px;
  // background: #ee0a24;
  color: #ee0a24;
  // border-radius: 22px;
  font-size: 14px;
  z-index: 1;
  // box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 强制右侧内容右对齐 */
.custom-value {
  text-align: right !important;
}

/* 底部固定容器 */  
.fixed-footer {  
  position: fixed;  
  bottom: 0;  
  left: 0;  
  right: 0;  
  padding: 12px 16px;  
  background: #fff;  
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);  

  /* Flex 布局实现按钮靠右 */  
  display: flex;  
  justify-content: flex-end;  
  gap: 12px; /* 按钮间距 */  
}  
/* 覆盖 Vant 按钮默认块级样式 */
.button-group .van-button {
  display: inline-flex;
  border-radius: 20px !important;     /* 设置圆角半径 ‌:ml-citation{ref="5,7" data="citationList"} */
    /* 自定义边框 ‌:ml-citation{ref="4,5" data="citationList"} */
}
.custom-btn-cancel {
  // border-radius: 20px;
  // border: 0px solid #FF7430 !important;
  height: 32px;
  width: 85px;
  border: 1px solid #ccc !important;
  display: flex !important;            /* 启用 Flex 布局 */
  justify-content: center !important;  /* 水平居中 */
  align-items: center !important;      /* 垂直居中 */
  font-size: 12px;
}
.custom-btn-refund {
  // border-radius: 20px;
  // border: 0px solid #FF7430 !important;
  height: 32px;
  width: 110px;
  border: 1px solid #01CDA7 !important;
  color: #01CDA7 !important;
  display: flex !important;            /* 启用 Flex 布局 */
  justify-content: center !important;  /* 水平居中 */
  align-items: center !important;      /* 垂直居中 */
  font-size: 12px;
margin-top: -5px;
  
  
}
.custom-btn {
  // border-radius: 20px;
  // border: 0px solid #FF7430 !important;
  height: 32px;
  width: 85px;
  background:#FF7430;
  display: flex !important;            /* 启用 Flex 布局 */
  justify-content: center !important;  /* 水平居中 */
  align-items: center !important;      /* 垂直居中 */
  font-size: 12px;
  border: none;      /* 移除边框 */
  outline: none;     /* 移除聚焦时的轮廓线 */
  margin-left: 8px;
}
.custom-btn-qr {
  border-radius: 20px;
  // border: 0px solid #FF7430 !important;
  height: 32px;
  width: 120px;
  background:#FF7430;
  display: flex !important;            /* 启用 Flex 布局 */
  justify-content: center !important;  /* 水平居中 */
  align-items: center !important;      /* 垂直居中 */
  font-size: 12px;
  border: none;      /* 移除边框 */
  outline: none;     /* 移除聚焦时的轮廓线 */
  margin-left: 8px;
}
/* 兼容 iOS 安全区域 */  
@supports (padding-bottom: env(safe-area-inset-bottom)) {  
  .fixed-footer {  
    padding-bottom: calc(12px + env(safe-area-inset-bottom));  
  }  
}  


  }
</style>
