/**
 * 严肃声明：
 * 开源版本请务必保留此注释头信息，若删除我方将保留所有法律责任追究！
 * 本系统已申请软件著作权，受国家版权局知识产权以及国家计算机软件著作权保护！
 * 可正常分享和学习源码，不得用于违法犯罪活动，违者必究！
 * Copyright (c) 2020 陈尼克 all rights reserved.
 * 版权所有，侵权必究！
 */
 
 @import './base.less';
 @primary: #1baeae; // 主题色
 @orange: #FF6B01; 
 @bc: #F7F7F7;
 @fc:#fff;
 
 // // 背景图片地址和大小
 .bis(@url) {
   background-image: url(@url);
   background-repeat: no-repeat;
   background-size: 100% 100%;
 }
 
 // //圆角
 .borderRadius(@radius) {
   -webkit-border-radius: @radius;
   -moz-border-radius: @radius;
   -ms-border-radius: @radius;
   -o-border-radius: @radius;
   border-radius: @radius;
 }
 
 // //1px底部边框
 .border-1px(@color){
   position: relative;
   &:after{
     display: block;
     position: absolute;
     left: 0;
     bottom: 0;
     width: 100%;
     border-top: 1px solid @color;
     content: '';
   }
 }
 // //定位全屏
 .allcover{
   position:absolute;
   top:0;
   right:0;
 }
 
 // //定位上下左右居中
 .center {
   position: absolute;
   top: 50%;
   left: 50%;
   transform: translate(-50%, -50%);
 }
 
 // //定位上下居中
 .ct {
   position: absolute;
   top: 50%;
   transform: translateY(-50%);
 }
 
 // //定位左右居中
 .cl {
   position: absolute;
   left: 50%;
   transform: translateX(-50%);
 }
 
 // //宽高
 .wh(@width, @height){
   width: @width;
   height: @height;
 }
 
 // //字体大小，颜色
 .sc(@size, @color){
   font-size: @size;
   color: @color;
 }
 
 .boxSizing {
   -webkit-box-sizing: border-box;
   -moz-box-sizing: border-box;
   box-sizing: border-box;
 }
 
 // //flex 布局和 子元素 对其方式
 .fj(@type: space-between){
   display: flex;
   justify-content: @type;
 }
 