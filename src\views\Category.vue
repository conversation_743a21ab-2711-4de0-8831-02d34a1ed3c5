<template>

    <div>
      <div class="top-category">
        <div class="scroll-wrapper">
          <ul class="category-list">
            <li
              v-for="item in state.categoryData"
              :key="item.categoryId"
              :class="{'active' : state.currentIndex == item.categoryId}"
              @click="selectMenu(item.categoryId)"
            >
              <img :src="item.categoryImg" class="category-icon" />
              <span v-text="item.categoryName"></span>
            </li>
          </ul>
        </div>
      </div>
      <div class="search-wrap" ref="searchWrap">
        <div class="left-category">
          <div 
            class="category-item" 
            v-for="(subCategory, subIndex) in state.currentCategory?.secondLevelCategoryVOs" 
            :key="subIndex"
            :class="{'active': state.currentSecondIndex === subIndex}"
            @click="selectSecondCategory(subIndex, subCategory)"
          >
            {{subCategory.categoryName}}
          </div>
        </div>
        <div class="right-content">
          <list-scroll 
            :scroll-data="currentProducts"
            :probe-type="3"
            :pullup="true"
            @scrollToEnd="onScrollBottom"
          >
            <div class="product-list">
              <div class="product-item" 
                v-for="(product, index) in currentProducts" 
                :key="index" 
                @click="selectProduct(product)">
                <img :src="product.goodsCoverImg" class="product-img"/>
                <div class="product-info">
                  <p class="product-title">{{product.goodsName}}</p>
                  <div class="product-bottom">
                    <p class="product-price">
                      {{Math.floor(product.goodsSellingPrice)}}<span class="decimal">.{{(product.goodsSellingPrice % 1).toFixed(1).slice(2)}}</span>
                    </p>
                    <p class="product-sales">{{product.goodsSellCount}}</p>
                  </div>
                </div>
              </div>
            </div>
            <!-- 添加底部加载状态 -->
            <!-- <div class="loading-status" v-if="state.loading || state.finished">
              {{ state.finished ? '没有更多了' : '加载中...' }}
            </div> -->
          </list-scroll>
        </div>
      </div>
    </div>

</template>

<script setup>
import { reactive, onMounted, ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import listScroll from '@/components/ListScroll.vue'
import { getCategory, search } from "@/service/good"
import { showLoadingToast, closeToast } from 'vant'
import { handleImageUrl } from '@/utils/tools';

const router = useRouter()
const searchWrap = ref(null)
const state = reactive({
  categoryData: [],
  currentIndex: 17,
  currentSecondIndex: 0,
  currentCategory: null,
  goodsList: [],
  // 添加分页相关状态
  pageNumber: 1,
  totalPage: 1,
  loading: false,
  finished: false
})

onMounted(async () => {
  let $screenHeight = document.documentElement.clientHeight
  searchWrap.value.style.height = $screenHeight - 100 + 'px'
  showLoadingToast('加载中...')
  
  try {
    const { data } = await getCategory()
    state.categoryData = data
    
    // 初始化当前分类
    if (data && data.length > 0) {
      state.currentCategory = data.find(item => item.categoryId === state.currentIndex) || data[0]
      state.currentIndex = state.currentCategory.categoryId
      
      // 获取第一个二级分类
      if (state.currentCategory.secondLevelCategoryVOs?.length > 0) {
        const firstSubCategory = state.currentCategory.secondLevelCategoryVOs[0]
        // 加载该分类下的商品
        await loadGoodsList(firstSubCategory)
      }
    }
  } catch (error) {
    console.error('初始化数据失败:', error)
  }
  
  closeToast()
})

const goHome = () => {
  router.push({ path: 'home' })
}

const selectMenu = async (index) => {
  state.currentIndex = index
  state.currentCategory = state.categoryData.find(item => item.categoryId === index)
  state.currentSecondIndex = 0
  
  // 加载新分类下的第一个二级分类商品
  if (state.currentCategory?.secondLevelCategoryVOs?.length > 0) {
    const firstSubCategory = state.currentCategory.secondLevelCategoryVOs[0]
    // 重置分页状态
    state.pageNumber = 1
    state.finished = false
    state.goodsList = []
    // 加载商品
    await loadGoodsList(firstSubCategory)
  }
}


const selectSecondCategory = async (index, category) => {
  state.currentSecondIndex = index
  state.pageNumber = 1 // 重置页码
  state.finished = false // 重置加载状态
  state.goodsList = [] // 清空商品列表
  await loadGoodsList(category)
}

// 添加加载商品列表的方法
const loadGoodsList = async (category) => {
  if (state.finished || state.loading) return
  
  state.loading = true
  
  try {
    const params = {
      goodsCategoryId: category.categoryId,
      orderBy: 'desc',
      pageNumber: state.pageNumber,
    }
    const { data } = await search(params)
    console.log('API返回的商品数据:', data)
    
    // 合并数据
    state.goodsList = [...state.goodsList, ...(data.list || [])]
    state.totalPage = data.totalPage || 1
    
    // 判断是否加载完成
    if (state.pageNumber >= state.totalPage) {
      state.finished = true
    } else {
      state.pageNumber++
    }
  } catch (error) {
    console.error('获取商品列表失败:', error)
  }
  
  state.loading = false
}

// 修改计算属性，返回商品列表
const currentProducts = computed(() => {
  console.log('完整商品列表数据:', state.goodsList)
  console.log('第一个商品数据:', state.goodsList[0])
  return state.goodsList
})

const selectProduct = (item) => {
  router.push({ path: `product/${item.goodsId}` })
}


// 修改滚动到底部的处理函数
const onScrollBottom = () => {
  console.log('触发滚动到底部事件')
  if (!state.finished) {
    console.log('触发滚动到底部事件',!state.finished)
    loadGoodsList(state.currentCategory.secondLevelCategoryVOs[state.currentSecondIndex])
  }
}


</script>

<style lang="less" scoped>
  @import '../common/style/mixin';
  .fade-out-enter-active, .fade-out-leave-active {
    // transition: opacity 0.5s;
  }

  .fade-out-enter, .fade-out-leave-to {
    // opacity: 0;
  }

  .search-wrap {
  display: flex;
  width: 100%;
  margin-top: 0;
  background: #fff;
  height: calc(100vh - 100px); // 修改：设置高度为视口高度减去顶部分类高度
  .left-category {
    width: 25%;
    height: 100%;
    background: #f8f8f8;
    overflow-y: auto;

    .category-item {
      padding: 15px 10px;
      font-size: 14px;
      font-family: PingFang SC;
      color: #666666;
      text-align: center; 
      align-items: center;
      justify-content: center;
      border-bottom: 1px solid #f1f1f1;
      
      &.active {
        background: #fff;
        font-family: PingFang SC;
        font-size: 15px;
        color: #01CDA7;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 16px;
          background-color: #01CDA7;
          border-radius: 2px;
        }
      }
    }
  }

  .right-content {
    width: 72%;
    height: 100%;
    position: relative;
    margin-right: 11px; 
    background: #fff;
    overflow: hidden;

    .scroll-wrapper {
      width: 100%;
      height: 100%;
      .product-list {
        width: 100%;
        padding: 10px;
        // 添加下面这行确保内容可以完全滚动到底部
        padding-bottom: 50px;
        min-height: 100%;
        .product-item {
          display: flex;
          width: 100%;
          height: auto;
          padding: 3px;
          margin-bottom: 15px;
          background: #fff;
          border-radius: 8px;

          .product-img {
            width: 90px;
            height: 90px;
            margin-right: 10px;  // 修改右边距
            object-fit: fill;
            border-radius: 4px;
          }

          .product-info {
            flex: 1;
            margin-right: 0;
            margin-left: 0;
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            .product-title {
              font-family: PingFang SC;
              font-weight: 500;
              font-size: 13px;
              color: #333333;
              margin-bottom: 10px;
              line-height: 1.4;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
            }

            .product-bottom {
              display: flex;
              align-items: baseline;
              justify-content: space-between;
              

              .product-price {
                  display: flex;
                  align-items: baseline;
                  font-family: PingFang SC;
                  font-size: 17px;
                  font-weight: 600;
                  color: #FA541C;
                  line-height: 17px;
                  margin-bottom:0%;
                  margin: 0;
                  &::before {
                    content: '¥';
                    font-weight: bold;
                    font-size: 12px;
                    margin-right: 2px;
                    vertical-align: 2px;  // 调整人民币符号的垂直对齐
                  }
                  .decimal {
                    font-size: 12px;
                    font-weight: bold;
                    vertical-align: top;  // 调整小数点的垂直对齐
                    margin-bottom: 0;
                  }
                }

              .product-sales {
                min-width: 60px;  // 设置最小宽度
                text-align: right;  // 文字右对齐
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 12px;
                color: #999999;
                line-height: 12px;  // 调整行高
                margin: 0;
                white-space: nowrap;  // 防止文字换行
                overflow: hidden;  // 超出隐藏
                text-overflow: ellipsis;  // 超出显示省略号

                &::before {
                    content: '销量：';
                    font-size: 12px;
                  }
              }
            }
          }
        }
      }
    }

    // 调整加载状态的位置
    .loading-status {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      background: #fff;
      z-index: 1;
    }
  }
}



  .top-category {
  width: 100%;
  height: 100px;
  background: #fff;
  border-bottom: 1px solid #f1f1f1;
  overflow: hidden;
  
  .scroll-wrapper {
    width: 100%;
    height: 100%;
    overflow-x: auto;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;
    
    &::-webkit-scrollbar {
      display: none;
    }
  }
  
  .category-list {
    display: flex;
    align-items: center;
    height: 100%;
    padding: 0 10px;
    white-space: nowrap;
    
    li {
      width: 60px;
      display: inline-flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 0 15px;
      font-size: 11px;
      color: #333;
      position: relative;
      height: 100%;
      
      .category-icon {
        position: relative;
        width: 45px;
        height: 45px;
        margin-bottom: 4px;
        object-fit: cover;
        border-radius: 50%;
        overflow: hidden;
        box-sizing: border-box;
        border: 1px solid transparent;  // 默认透明边框，避免选中时的抖动
        padding: 1px;
      }
      
      &.active {
        span {
          display: inline-block;
          padding: 3px 7px;  // 上下3px，左右7px的内边距
          background: #01CDA7;
          border-radius: 8.5px;  // (文字高度 + 上下内边距) ÷ 2 = (11 + 6) ÷ 2 = 8.5px
          color: #FFFFFF;
          font-size: 11px;
          line-height: 11px;  // 与字体大小一致
        }
        
        .category-icon {
          border-color: #01CDA7;  // 选中时改变边框颜色
        }
      }
    }
  }
}
.loading-status {
  text-align: center;
  padding: 10px 0;
  color: #999;
  font-size: 14px;
}
</style>7