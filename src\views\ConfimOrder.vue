<!--
 * 严肃声明：
 * 开源版本请务必保留此注释头信息，若删除我方将保留所有法律责任追究！
 * 本系统已申请软件著作权，受国家版权局知识产权以及国家计算机软件著作权保护！
 * 可正常分享和学习源码，不得用于违法犯罪活动，违者必究！
 * Copyright (c) 2020 陈尼克 all rights reserved.
 * 版权所有，侵权必究！
 *


* <template>
*   <div class="order-box">
*     <s-header :name="'订单确认'" :back="'/user'"></s-header>
*     <view class="van-ellipsis">请确认订单信息</view>
*     <div class="content">
     
          
            <van-card
              :num="state.list.goodsCount"
              :price="state.list.sellingPrice"
              desc="全场包邮"
              :title="state.list.goodsName"
              :thumb="$filters.prefix(state.list.goodsCoverImg)"
          >
        
          <template #footer>  
    <van-button size="mini" @click="count > 1 ? count-- : count">-</van-button>  
    <span class="count">{{ count }}</span>  
    <van-button size="mini" @click="count++">+</van-button>  
  </template>  
        
        </van-card>
   
*     </div>
*   
*   <div class="fixed-footer">  
*     <span class="footer-text">{{ totalPrice }}</span>  
*     <van-button type="primary" size="small" block @click="handlePayOrder(state.detail.orderNo)">确认并付款</van-button>  
*   </div>  
*   </div>
* </template>
* 
* <script setup>
import { reactive, ref,computed  } from 'vue';
import sHeader from '@/components/SimpleHeader.vue'
import { getOrderList } from '@/service/order'
import { useRouter } from 'vue-router'

const router = useRouter()
const state = reactive({
  status: '',
  loading: false,
  finished: false,
  refreshing: false,
  list: {
    goodsCount: 1,
    sellingPrice: 0,
    goodsName: '',
    goodsCoverImg: ''
  },
  page: 1,
  totalPage: 0
})
const count = ref(1);  // 商品数量  
const price = 299;     // 商品单价（固定值）  
// 计算总价  
const totalPrice = computed(() => (count.value * price).toFixed(2));  
const loadData = async () => {
  const { data, data: { list } } = await getOrderList({ pageNumber: state.page, status: state.status })
  state.list = state.list.concat(list)
  state.totalPage = data.totalPage
  state.loading = false;
  if (state.page >= data.totalPage) state.finished = true
}

const onChangeTab = ({ name }) => {
  // 这里 Tab 最好采用点击事件，@click，如果用 @change 事件，会默认进来执行一次。
  state.status = name
  onRefresh()
}

const goTo = (id) => {
  router.push({ path: '/order-detail', query: { id } })
}

const goBack = () => {
  router.go(-1)
}

const handlePayOrder = async (id) => {
  await payOrder({ orderNo: id})
  // state.showPay = false
  // init()
  showSuccessToast('支付成功')
  setTimeout(() => {
    router.push({ path: '/order' })
  }, 2000)
}

const onLoad = () => {
  if (!state.refreshing && state.page < state.totalPage) {
    console.log(state.page)
    console.log(state.totalPage)
    state.page = state.page + 1
  }
  if (state.refreshing) {
    state.list = [];
    state.refreshing = false;
  }
  loadData()
}

const onRefresh = () => {
  state.refreshing = true
  state.finished = false
  state.loading = true
  state.page = 1
  onLoad()
}
* </script>
* 
* <style lang="less" scoped>
  @import '../common/style/mixin';
  .order-box {
    .order-header {
      position: fixed;
      top: 0;
      left: 0;
      z-index: 10000;
      .fj();
      .wh(100%, 44px);
      line-height: 44px;
      padding: 0 10px;
      .boxSizing();
      color: #252525;
      background: #fff;
      border-bottom: 1px solid #dcdcdc;
      .order-name {
        font-size: 14px;
      }
    }
    .order-tab {
      position: fixed;
      left: 0;
      z-index: 1000;
      width: 100%;
      border-bottom: 1px solid #e9e9e9;
    }
    .skeleton {
      margin-top: 60px;
    }
    .content {
      height: calc(~"(100vh - 70px)");
      overflow: hidden;
      overflow-y: scroll; 
      margin-top: 34px;

/* 修复价格区域间距 */  
:deep(.van-card__bottom) {  
  padding-top: 8px !important;  
}  
/* 自定义按钮样式 */  
.van-card__footer {  
  display: flex;  
  align-items: center;  
  gap: 10px;  
}  
.count {  
  min-width: 30px;  
  text-align: center;  
}  


    }
    .order-list-refresh {
      .van-card__content {
        display: flex;
        flex-direction: column;
        justify-content: center;
      }
      .van-pull-refresh__head {
        background: #f9f9f9;
      }
      .order-item-box {
        margin: 20px 10px;
        background-color: #fff;
        .order-item-header {
          padding: 10px 20px 0 20px;
          display: flex;
          justify-content: space-between;
        }
        .van-card {
          background-color: #fff;
          margin-top: 0;
        }
      }
    }
    /* 固定底部容器 */  
.fixed-footer {  
  position: fixed;  
  bottom: 0;  
  left: 0;  
  right: 0;  
  padding: 12px 16px;  
  background: #fff;  
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);  

  /* Flex 布局实现左右分离 */  
  display: flex;  
  justify-content: space-between;  
  align-items: center;  
}  

/* 适配 iOS 安全区域 */  
@supports (padding-bottom: env(safe-area-inset-bottom)) {  
  .fixed-footer {  
    padding-bottom: calc(12px + env(safe-area-inset-bottom));  
  }  
}  
  }
* </style>
-->