/**
 * 严肃声明：
 * 开源版本请务必保留此注释头信息，若删除我方将保留所有法律责任追究！
 * 本系统已申请软件著作权，受国家版权局知识产权以及国家计算机软件著作权保护！
 * 可正常分享和学习源码，不得用于违法犯罪活动，违者必究！
 * Copyright (c) 2020 陈尼克 all rights reserved.
 * 版权所有，侵权必究！
 */
 
// *{
//   margin: 0;
//   padding: 0;
// }
//移动端点击高亮
html,body{
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
input{
  border: none;
  outline: none;
  -webkit-appearance: none;
  -webkit-appearance: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
textarea{
  border: none;
  outline: none;
}
button{
  border: none;
  outline: none;
}
a{
  text-decoration: none;
  color: #333;
}
li{
  list-style-type: none;
}
//解决端遮罩层穿透
body.dialog-open {
  position: fixed;
  width: 100%;
}
.page{
  padding: 0 50px;
  width: 100%;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
