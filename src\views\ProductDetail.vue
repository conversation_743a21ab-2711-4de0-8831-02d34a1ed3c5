<!--
 * 严肃声明：
 * 开源版本请务必保留此注释头信息，若删除我方将保留所有法律责任追究！
 * 本系统已申请软件著作权，受国家版权局知识产权以及国家计算机软件著作权保护！
 * 可正常分享和学习源码，不得用于违法犯罪活动，违者必究！
 * Copyright (c) 2020 陈尼克 all rights reserved.
 * 版权所有，侵权必究！
 *
-->

<template>
  <div class="product-detail">
    <!-- <s-header :name="'商品详情'"></s-header> -->
    <div class="detail-content">
      <!-- <div class="detail-swipe-wrap">
        <van-swipe class="my-swipe" indicator-color="#1baeae">
          <van-swipe-item v-for="(item, index) in state.detail.goodsCarouselList" :key="index">
            <img :src="item" alt="">
          </van-swipe-item>
        </van-swipe>
      </div> -->
      <div class="detail-swipe-wrap">
        <van-swipe class="my-swipe" :show-indicators="false">
          <van-swipe-item v-for="(item, index) in state.detail.goodsCarouselList" :key="index">
            <div class="swipe-img-wrap">
              <img :src="item" alt="" @click="previewImage(index)">
            </div>
          </van-swipe-item>
          <template #indicator="{ active, total }">
            <div class="custom-indicator">
              {{ active + 1 }}/{{ total }}
            </div>
          </template>
        </van-swipe>
      </div>

      <div class="product-info">

        <div class="product-price">
          <div class="price-info">
            <span class="selling-price">
              {{ Math.floor(state.skuPrice|| 0) }}<span class="decimal">.{{ ((state.skuPrice || 0) % 1).toFixed(2).slice(2) }}</span>
            </span>
            <span class="original-price">{{state.goodsOriginalPrice || '' }}</span>
          </div>
          <span class="sales">已售 {{ state.detail.goodsSellCount || 0 }}</span>
        </div>
        <div class="product-tag" v-if="state.detail.goodsTag">{{ state.detail.goodsTag }}</div>
        <div class="product-title">
          {{ state.detail.goodsName || '' }}
        </div>
        <div class="product-subtitle">
          {{ state.detail.goodsIntro || '' }}
        </div>
      </div>
      <div class="product-intro">
        <!-- <ul>
          <li>概述</li>
          <li>参数</li>
          <li>安装服务</li>
          <li>常见问题</li>
        </ul> -->
        <div class="product-content" v-html="state.detail.goodsDetailContent || ''"></div>
      </div>
    </div>
    <!-- 添加SKU选择弹窗 -->
    <van-popup
      v-model:show="state.showSku"
      position="bottom"
      round
      closeable
      :style="{ maxHeight: '80%' , marginTop: '-20px'}"
    >
      <div class="sku-popup">
        <div class="sku-header">
          <div class="sku-image">
            <img :src="state.skuImage || state.detail.goodsCoverImg" alt="">
          </div>
          <div class="sku-info">
            <div class="sku-price">¥{{ formatPrice(state.currentPrice)}}</div>
            <div class="sku-title">{{ state.detail.goodsName }}</div>
            <!-- <div class="sku-selected" v-if="state.selectedSpecs.length > 0">已选：{{ state.selectedSpecs.join('，') }}</div> -->
          </div>
          <!-- 数量选择 -->
          <div class="quantity-selector">
            <van-stepper v-model="state.quantity" min="1" max="99" @change="updatePrice" @click.native.stop />
          </div>

        </div>
        
        <!-- SKU规格选择 -->
        <!-- SKU规格选择 -->
        <div class="sku-options" v-if="state.skuAttributes.length > 0" style="height: calc(70% - 10px); overflow-y: auto; padding-bottom: 10px;">
          <div v-for="(attr, attrIndex) in state.skuAttributes" :key="attrIndex" class="sku-option-group">
            <div class="sku-option-title">{{ attr.name }}</div>
            <div class="sku-option-values">
              <div 
                v-for="(value, valueIndex) in attr.values" 
                :key="valueIndex"
                :class="['sku-option-value', { active: isSpecSelected(attr.name, value) }]"
                @click="selectSpec(attr.name, value)"
              >
                {{ value }}
              </div>
            </div>
          </div>
        </div>
        
        <van-action-bar>
      <!-- <van-action-bar-icon icon="chat-o" text="客服" /> -->
      <!-- <van-action-bar-icon icon="cart-o" :badge="!cart.count ? '' : cart.count" @click="goTo()" text="购物车" /> -->
      <!-- <van-action-bar-button type="warning" @click="handleAddCart" text="加入购物车" /> -->
      <van-action-bar-button type="danger" @click="confirmSelection" text="确定" />
    </van-action-bar>
        
        <!-- <div class="sku-actions">
          <van-action-bar-button round block type="danger" @click="confirmSelection">确定</van-button>
        </div> -->
      </div>
    </van-popup>
    
    <van-action-bar>
      <!-- <van-action-bar-icon icon="chat-o" text="客服" /> -->
      <!-- <van-action-bar-icon icon="cart-o" :badge="!cart.count ? '' : cart.count" @click="goTo()" text="购物车" /> -->
      <!-- <van-action-bar-button type="warning" @click="handleAddCart" text="加入购物车" /> -->
      <van-action-bar-button type="danger" @click="goToCart" text="立即购买" />
    </van-action-bar>
  </div>
</template>

<script setup>
import { reactive, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getDetail ,multiSpecification} from '@/service/good'
import sHeader from '@/components/SimpleHeader.vue'
import { handleImageUrl } from '@/utils/tools';
import { showImagePreview, showToast } from 'vant';
const route = useRoute()
const router = useRouter()
// const cart = useCartStore()

// const showQuantityPopup = ref(false);
// const tempQuantity = ref(1);

const state = reactive({
  detail: {
    goodsCarouselList: [],
    skuList: [],
    goodsSpecificationList: []
  },
  goodsOriginalPrice: '',
  skuImage: '',
  skuPrice: 0,
  showSku: false,
  quantity: 1,
  currentPrice: 0,
  selectedSpecs: [],
  currentSku: null,
  skuAttributes: []
})

// 格式化价格显示
const formatPrice = (price) => {
  console.log( 'price',state.currentPrice)
  return price.toFixed(2)     // 小数保留两位（如：99.90）
}
// 更新价格的方法
const updatePrice = () => {
    state.currentPrice = (state.skuPrice * state.quantity);
};

onMounted(async () => {
  const { id } = route.params
  const { data } = await getDetail(id)
  state.detail = data
  
  // 解析SKU属性
  if (state.detail.goodsSpecificationList && state.detail.goodsSpecificationList.length > 0) {
    parseSkuAttributes()
  }

  // 默认选择每个属性的第一个选项
  state.skuAttributes.forEach(attr => {
    if (attr.values.length > 0) {
      const defaultSpec = `${attr.name}:${attr.values[0]}`;
      state.selectedSpecs.push(defaultSpec);
    }
  });

  // 更新当前选中的SKU
  updateCurrentSku();
})

// 解析SKU属性
const parseSkuAttributes = () => {
  const attributes = {};
  
  // 使用goodsSpecificationList解析SKU属性
  if (state.detail.goodsSpecificationList) {
    state.detail.goodsSpecificationList.forEach(spec => {
      if (!attributes[spec.specification]) {
        attributes[spec.specification] = new Set();
      }
      attributes[spec.specification].add(spec.attribute);
    });
  }
  // 兼容旧的skuList解析方式
  else if (state.detail.skuList) {
    state.detail.skuList.forEach(sku => {
      if (sku.specs) {
        const specs = sku.specs.split(',');
        specs.forEach(spec => {
          const [name, value] = spec.split(':');
          if (!attributes[name]) {
            attributes[name] = new Set();
          }
          attributes[name].add(value);
        });
      }
    });
  }
  
  state.skuAttributes = Object.keys(attributes).map(name => ({
    name,
    values: Array.from(attributes[name])
  }));
}

// 检查规格是否已选择
const isSpecSelected = (name, value) => {
  const specString = `${name}:${value}`;
  return state.selectedSpecs.includes(specString);
}

// 选择规格
const selectSpec = (name, value) => {
  const specString = `${name}:${value}`;
  
  // 移除同类规格
  state.selectedSpecs = state.selectedSpecs.filter(spec => !spec.startsWith(`${name}:`));
  
  // 添加新选择的规格
  state.selectedSpecs.push(specString);
  
  // 更新当前选中的SKU
  updateCurrentSku();
}

// 更新当前选中的SKU
const updateCurrentSku = async () => {
  if (state.selectedSpecs.length === 0) {
    state.currentSku = null;
    return;
  }
  
  // 如果有skuList，使用skuList查找匹配的SKU
  if (state.detail.skuList && state.detail.skuList.length > 0) {
    state.currentSku = state.detail.skuList.find(sku => {
      if (!sku.specs) return false;
      
      const skuSpecs = sku.specs.split(',');
      return state.selectedSpecs.every(selected => skuSpecs.includes(selected));
    });
  } else {
    // 如果没有skuList但有goodsSpecificationList，则尝试从其他数据中获取SKU信息
    // 这里可能需要根据实际API返回的数据结构进行调整
    state.currentSku = null;
  }
  // 构造规格字符串
  const specsString = state.selectedSpecs.join(' ');
  // 调用 multiSpecification 接口更新价格
  const { data } = await multiSpecification(state.detail.goodsId, specsString);
  console.log('data',data.goodsOriginalPrice)
  console.log('goodsSellingPrice',data.goodsSellingPrice)
  if (data) {
        state.skuPrice = data.goodsSellingPrice; 
        state.skuImage = data.img; 
        state.goodsOriginalPrice = data.goodsOriginalPrice; 
        updatePrice();
    }
}

// 立即购买按钮点击
const goToCart = () => {
  // if (state.detail.goodsSpecificationList && state.detail.goodsSpecificationList.length > 0) {
    // 有SKU，显示选择弹窗
    state.showSku = true;
    // tempQuantity.value = state.quantity;
//   } else {
//     // 无SKU，直接进入订单确认页
//     router.push({ path: `/order-confirm/${state.detail.goodsId}` });
//   }
}

// 确认SKU选择
const confirmSelection = () => {
  // 检查是否已选择所有必要的规格
  if (state.skuAttributes.length > 0 && state.selectedSpecs.length < state.skuAttributes.length) {
    showToast('请选择完整规格');
    return;
  }

  state.showSku = false;
  
  const query = { 
    quantity: state.quantity
  };
  
  // 如果有SKU ID，添加到查询参数
  
  // 如果没有SKU ID但有选择的规格，将规格信息添加到查询参数
  if (state.selectedSpecs.length > 0) {
    query.specs = state.selectedSpecs.join(' ');
  }
  query.goodsName = state.detail.goodsName;
  router.push({ 
    path: `/order-confirm/${state.detail.goodsId}`,
    query: query
  });
}

// // 取消修改数量
// const cancelQuantity = () => {
//   showQuantityPopup.value = false;
// }

// // 确认修改数量
// const confirmQuantity = () => {
//   state.quantity = tempQuantity.value;
//   showQuantityPopup.value = false;
// }

nextTick(() => {
  // 一些和DOM有关的东西
  const content = document.querySelector('.detail-content')
  content.scrollTop = 0
})

const goBack = () => {
  router.go(-1)
}

const goTo = () => {
  router.push({ path: '/cart' })
}

const previewImage = (index) => {
  showImagePreview({
    images: state.detail.goodsCarouselList,
    startPosition: index,
    closeable: true,
    showIndex: true,  // 显示图片索引
    className: "custom-preview",
  });
}

</script>

<style lang="less">
  @import '../common/style/mixin';
  .product-detail {
    .detail-header {
      position: fixed;
      top: 0;
      left: 0;
      z-index: 10000;
      .fj();
      .wh(100%, 44px);
      line-height: 44px;
      padding: 0 10px;
      .boxSizing();
      color: #252525;
      background: #fff;
      border-bottom: 1px solid #dcdcdc;
      .product-name {
        font-size: 14px;
      }
    }
    .detail-content {
      height: calc(100vh - 68px);
      overflow: hidden;
      overflow-y: auto;
      .detail-swipe-wrap {
        position: relative;
        width: 100%;
        height: 375px;
        background: #FFFFFF;
        .swipe-img-wrap {
          width: 100%;
          height: 375px;
          display: flex;
          align-items: center;
          justify-content: center;
          img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;  // 保持图片比例并完整显示
            width: auto;         // 让图片保持原始宽高比
            height: auto;        // 让图片保持原始宽高比
          }
        }
        // ... 其他样式保持不变 ...
        .custom-indicator {
          position: absolute;
          right: 15px;
          bottom: 15px;
          width: 35px;
          height: 21px;
          background: #8C8C8C;
          border-radius: 11px;
          border: 0px solid #5A5A5A;
          opacity: 0.4;
          color: #fff;
          font-size: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      .product-info {
        width: 375px;
        height: auto;
        padding: 0 10px;
        margin-top: 10px;
        .product-title {
          text-align: left;
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 15px;
          color: #333333;
        }
        .product-subtitle {
          margin-top: 10px;
          font-size: 14px;
          color: #666;
          text-align: left;
        }
        .product-tag {
          display: inline-block;
          padding: 2px 12px;
          background: rgba(1, 205, 167, 0.1);
          border-radius: 12px;
          font-size: 12px;
          color: #01CDA7;
          margin: 8px 0;
        }
        .product-price {
          .fj();
          align-items: baseline;  // 修改为 baseline 对齐
          .price-info {
              display: flex;
              align-items: baseline;
              .selling-price {
                  font-family: PingFang SC;
                  font-size: 24px;
                  font-weight: 600;
                  color: #FA541C;
                  line-height: 24px;
                  &::before {
                    content: '¥';
                    font-size: 14px;
                    font-weight: 600;
                    margin-right: 2px;
                  }
                  .decimal {
                    font-size: 14px;
                    font-weight: 600;
                  }
                }
            .original-price {
              color: #999;
              font-size: 14px;
              text-decoration: line-through;
              margin-left: 8px;
              &::before {
                    content: '¥';
                  }
            }
          }
          .sales {
            width: 74px;
            height: 11px;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 12px;
            color: #999999;
            // 删除之前的 height 和 line-height 属性
          }
        }
      }
      .product-intro {
        width: 100%;
        padding-bottom: 50px;
        margin-top: 10px; 
        // ul {
        //   .fj();
        //   width: 100%;
        //   margin: 10px 0;
        //   li {
        //     flex: 1;
        //     padding: 5px 0;
        //     text-align: center;
        //     font-size: 15px;
        //     border-right: 1px solid #999;
        //     box-sizing: border-box;
        //     &:last-child {
        //       border-right: none;
        //     }
        //   }
        // }
        .product-content {
          padding: 0 20px;
          
          // 富文本样式
          :deep(p) {
            margin: 10px 0;
            line-height: 1.6;
            font-size: 14px;
            color: #333;
          }
          
          :deep(img) {
            max-width: 100%;
            height: auto;
            margin: 10px 0;
            display: block;
          }
          
          :deep(table) {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
            
            th, td {
              border: 1px solid #eee;
              padding: 8px;
              text-align: left;
            }
          }
          
          :deep(ul), :deep(ol) {
            padding-left: 20px;
            margin: 10px 0;
            
            li {
              margin: 5px 0;
              line-height: 1.6;
            }
          }
        }
       .product-introimg {
          width: 100%;
          img {
            width: 100%;
            height: 100%;
          }
        }
      }
    }
    .van-action-bar-button--warning {
      background: linear-gradient(to right,#6bd8d8, @primary)
    }
    .van-action-bar-button--danger {
      background: linear-gradient(90deg, #FF7430 0%, #FF9D2E 100%);
      border-radius: 19px;
      border: none;  // 移除边框
      margin-right: 15px;  // 添加右边距
      margin-top: 9px;
      margin-bottom: 45px;
      margin-left: 15px;
      height: 39px;
      width: 63px;
      font-family: PingFang SC;
      font-size: 16px;
      color: #FFFFFF;
      line-height: 15px; 
    }
  }
  
  .custom-preview {
    width:100%;
    height: 100%;
    background: rgba(255, 0, 0, 0);
  }
</style>

<style lang="less" scoped>
// 添加SKU弹窗样式
.sku-popup {
  padding: 20px 15px;
  
  .sku-header {
    display: flex;
    margin-bottom: 20px;
    position: relative;
    
    .sku-image {
      width: 80px;
      height: 80px;
      margin-right: 15px;
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 4px;
      }
    }
    
    .sku-info {
      flex: 1;
      
      .sku-price {
        color: #FA541C;
        font-size: 20px;
        font-weight: bold;
        margin-bottom: 5px;
      }
      
      .sku-title {
        font-size: 14px;
        margin-bottom: 5px;
      }
      
      .sku-selected {
        font-size: 12px;
        color: #999;
      }
    }
    
    .quantity-selector {
      position: absolute;
      right: 0;
      bottom: 0;
    }
  }
  
  .sku-options {
    margin-bottom: 20px;
    
    .sku-option-group {
      margin-bottom: 15px;
      
      .sku-option-title {
        font-size: 13px;
        font-weight: 500;
        margin-bottom: 10px;
        color: #333333;
      }
      
      .sku-option-values {
        display: flex;
        flex-wrap: wrap;
        
        .sku-option-value {
          padding: 6px 12px;
          margin: 0 10px 10px 0;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 12px;
          
          &.active {
            color: #1baeae;
            border-color: #1baeae;
            background-color: rgba(27, 174, 174, 0.05);
          }
        }
      }
    }
  }
  
    
    
    
    .quantity-selector {
      display: flex;
      align-items: bottom;
    }
  
  
  .sku-actions {
    margin-top: 20px;
  }
}

.quantity-popup {
  padding: 20px;
  
  .quantity-popup-title {
    text-align: center;
    font-size: 16px;
    margin-bottom: 20px;
  }
  
  .quantity-popup-content {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
  }
  
  .quantity-popup-actions {
    display: flex;
    gap: 10px;
    
    .van-button {
      flex: 1;
    }
  }
}
</style>
