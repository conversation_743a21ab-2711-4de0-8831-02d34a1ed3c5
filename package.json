{"name": "newbee-mall-h5-v3", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "build.pre": "vite build --mode prepro", "preview": "vite preview", "serve": "vite preview --host"}, "dependencies": {"axios": "^1.2.6", "better-scroll": "2.3.0", "js-md5": "^0.7.3", "lib-flexible": "^0.3.2", "pinia": "^2.0.28", "qrcode": "^1.5.4", "qrcodejs2": "0.0.2", "qrcodejs2-fix": "0.0.1", "vant": "^4.0.9", "vconsole": "^3.15.1", "vue": "^3.2.45", "vue-router": "^4.1.6"}, "devDependencies": {"@vitejs/plugin-vue": "^4.0.0", "less": "^4.1.3", "postcss-pxtorem": "^6.0.0", "unplugin-vue-components": "^0.23.0", "vite": "^4.0.0"}}