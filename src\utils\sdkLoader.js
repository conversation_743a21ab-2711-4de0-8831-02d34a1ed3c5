/**
 * 动态加载SDK脚本
 * @param {string} url 脚本URL
 * @returns {Promise} 
 */
function loadScript(url) {
    console.log(`开始加载SDK: ${url}`)
    return new Promise((resolve, reject) => {
      const script = document.createElement('script')
      script.src = url
      script.onload = () => {
        console.log(`SDK加载成功: ${url}`)
        resolve()
      }
      script.onerror = (error) => {
        console.error(`SDK加载失败: ${url}`, error)
        reject(error)
      }
      document.head.appendChild(script)
    })
  }
  
  /**
   * 根据环境加载对应SDK
   * @param {Function} callback 加载完成后的回调函数
   * @returns {Promise} 加载结果的Promise
   */
  export function loadEnvironmentSDK(callback) {
    console.log('检测当前环境...')
    let sdkPromises = []
    let environment = '普通浏览器'
    
    if (navigator.userAgent.indexOf("AliApp") > -1) {
      // 支付宝环境
      environment = '支付宝'
      console.log('检测到支付宝环境')
      sdkPromises.push(loadScript("https://appx/web-view.min.js"))
      sdkPromises.push(loadScript("https://gw.alipayobjects.com/as/g/h5-lib/alipayjsapi/3.1.1/alipayjsapi.min.js"))
    } else if (navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1) {
      // 微信环境
      environment = '微信'
      console.log('检测到微信环境')
      sdkPromises.push(loadScript("https://res.wx.qq.com/open/js/jweixin-1.6.0.js"))
    // } else if (navigator.userAgent.indexOf("smkVersion") > -1) {
      // // 市民卡环境
      // environment = '市民卡'
      // console.log('检测到市民卡环境')
      // sdkPromises.push(loadScript("https://open.iconntech.com/filesys/group1/M00/00/5E/wDIMGWFDC--AVFhUAADOSgQkvzA4741.js"))
    } else {
      console.log('未检测到特殊环境，使用普通浏览器模式')
    }
    
    if (sdkPromises.length > 0) {
      return Promise.all(sdkPromises)
        .then(() => {
          console.log(`${environment}环境SDK加载完成`)
          if (typeof callback === 'function') {
            callback(true, environment)
          }
          return { success: true, environment }
        })
        .catch((error) => {
          console.error(`${environment}环境SDK加载失败`, error)
          if (typeof callback === 'function') {
            callback(false, environment, error)
          }
          return { success: false, environment, error }
        })
    } else {
      console.log('无需加载特定SDK')
      if (typeof callback === 'function') {
        callback(true, environment)
      }
      return Promise.resolve({ success: true, environment })
    }
  }