import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import tools from './utils/tools'
import common from './utils/common'
import { getJtToken } from './service/api'
import { loadEnvironmentSDK } from './utils/sdkLoader'
import 'lib-flexible/flexible'
import Vant from 'vant'
import 'vant/lib/index.css'

import './assets/main.css'
import './common/style/theme.css'
import 'vant/es/toast/style'

import vconsole from "vconsole";
if (import.meta.env.VITE_API_ENV === 'TEST') {
    var v = new vconsole();
    console.log(v);
}

// 根据环境加载相应的SDK
// console.log('开始加载环境SDK...')
// loadEnvironmentSDK((success, environment, error) => {
//   if (success) {
//     console.log(`%c环境SDK加载成功: ${environment}`, 'color: green; font-weight: bold')
//   } else {
//     console.error(`环境SDK加载失败: ${environment}`, error)
//   }
// })

const app = createApp(App)
const pinia = createPinia()
app.use(pinia)

// 全局过滤器
app.config.globalProperties.$filters = {
    prefix(url) {
        return url
        if (url && url.startsWith('http')) {
            return url
        } else {
            url = `http://backend-api-01.newbee.ltd${url}`
            return url
        }
    }
}
app.use(Vant)
app.use(router)
app.mount('#app')
