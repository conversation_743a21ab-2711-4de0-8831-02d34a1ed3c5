<!--
 * 严肃声明：
 * 开源版本请务必保留此注释头信息，若删除我方将保留所有法律责任追究！
 * 本系统已申请软件著作权，受国家版权局知识产权以及国家计算机软件著作权保护！
 * 可正常分享和学习源码，不得用于违法犯罪活动，违者必究！
 * Copyright (c) 2020 陈尼克 all rights reserved.
 * 版权所有，侵权必究！
 *
-->

<template>
    <div class="order-confirm">
        <!-- <s-header :name="'确认订单'"></s-header> -->
        <div class="order-content">
            <div class="confirm-tip">请确认订单信息</div>
            <div class="product-card">
                <!-- <div class="supplier-info">
          <van-icon name="shop-o" />
          <span>{{ state.detail.supplierName || '商城自营' }}</span>
        </div> -->
                <div class="product-info">
                    <img
                        :src="state.detail.img"
                        class="product-img"
                    />
                    <div class="info-right">
                        <div class="product-name">
                            {{ state.goodsName }}
                        </div>
                        <div
                            class="product-spec"
                        >
                            规格:{{ state.detail.description }}
                        </div>
                        <div class="price-stepper">
                            <!-- <span class="dec">规格：自定义规格</span> -->
                            <van-stepper
                                v-model="state.quantity"
                                min="1"
                                @change="handleQuantityChange"
                            />
                        </div>
                    </div>
                </div>
            </div>
            <!-- <div class="price-detail">
        <div class="price-item">
          <span>商品金额</span>
          <span class="amount">¥{{ totalAmount }}</span>
        </div>
      </div> -->
        </div>
        <div class="bottom-action">
            <div class="total-price">
                <span class="price"
                    >{{
                        Math.floor(
                            (state.detail.goodsSellingPrice ||0) * state.quantity
                        )
                    }}<span class="decimal"
                        >.{{
                            (
                                ((state.detail.goodsSellingPrice ||0) *state.quantity) %1
                            )
                                .toFixed(2)
                                .slice(2)
                        }}</span
                    >
                </span>
            </div>
            <van-button type="danger" class="submit-btn" @click="handleSubmit"
                >确认并付款</van-button
            >
        </div>
    </div>
</template>

<script setup>
import { reactive, computed, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { getDetail, multiSpecification } from "@/service/good";
import { handleImageUrl } from "@/utils/tools";
import { showToast } from "vant";
import { createOrder, payOrder } from "@/service/order";
import { addCart } from "@/service/cart"; // 添加这行

const route = useRoute();
const router = useRouter();

const state = reactive({
    detail: {
        goodsCarouselList: [],
        skuList: [],
    },
    goodsName: "",
    quantity: 1,
    specs: null
});

const totalAmount = computed(() => {
    const price = state.detail.goodsSellingPrice;
    return (price * state.quantity).toFixed(2);
});

onMounted(async () => {
    const { id } = route.params;
    const { quantity, specs, goodsName } = route.query;
    state.goodsName = goodsName;
    // 解析规格参数
    let specsObj = {};
    let specsString = specs; // 使用新的变量来存储拼接后的字符串
    if (specs) {
        specs.split(",").forEach((spec) => {
            const [key, value] = spec.split(":");
            specsObj[key] = value;
        });
        // 将规格参数拼接成字符串
        specsString = Object.values(specsObj).join(' ');
    }

    console.log("specs", specsString);
    // 获取商品详情
    const { data } = await multiSpecification(id, specs);
    state.detail = data;
    //如果sku详情可以获取到数据，则使用这个数据当detail

    
    // 设置数量和SKU ID
    if (quantity) {
        state.quantity = parseInt(quantity);
    }
    
    if (specs) {
        state.specs = specs;
    }
});

const handleQuantityChange = (value) => {
    state.quantity = value;
};

const handleSubmit = async () => {
    try {
        // 先加入购物车
        const cartParams = {
            goodsCount: state.quantity,
            goodsId: state.detail.goodsId,
            goodsSpecification: state.specs,
        };
        

        
        const { data: cartData, resultCode } = await addCart(cartParams);

        if (resultCode !== 200) {
            showToast("加入购物车失败");
            return;
        }
        console.log("handleSubmit", cartData);

        // 构造支付参数
        const wxAppid = window.localStorage.getItem("wxAppid");
        const wxOpenid = window.localStorage.getItem("wxOpenid");
        const alipayUserId = window.localStorage.getItem("alipayUserId");
        // 根据环境构造remark2
        let remark2 = "";
        if (
            navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1 &&
            wxAppid &&
            wxOpenid
        ) {
            // 微信环境
            remark2 = JSON.stringify({
                openid: wxOpenid,
                appid: wxAppid,
            });
        } else if (navigator.userAgent.indexOf("AliApp") > -1 && alipayUserId) {
            //支付宝
            // showToast("未获取到用户信息");
            // 支付宝环境
            remark2 = JSON.stringify({
                openid: alipayUserId,
            });
        } else {
            showToast("未获取到用户wxAppid和wxOpenid");
            return;
        }

        console.log("remark2remark2remark2remark2", remark2);

        const baseUrl = location.origin + location.pathname + "#/order-detail";
        // 创建订单
        const params = {
            cartItemIds: [Number(cartData)],
            callBackUrl: baseUrl,
            remark2,
        };

        const res = await createOrder(params);
        console.log("handleSubmit2", res);
        if (res.resultCode === 200 && res.data?.cashierUrl) {
            // 跳转到收银台
            console.log("res.datares.datares.data", res.data);
            window.location.href = res.data.cashierUrl;
        } else {
            showToast("拉起收银台失败");
        }
    } catch (error) {
        showToast("下单失败");
    }
};
</script>

<style lang="less" scoped>
.order-confirm {
    min-height: 100vh;
    background: #f7f7f7;
    padding-bottom: 50px;

    .order-content {
        padding: 10px 15px;

        .confirm-tip {
            margin-left: 0;
            margin-top: 15px;
            width: auto;
            height: 13px;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #333333;
            margin-bottom: 14px;
            //   padding-left: 12px;  // 与卡片内边距对齐
            line-height: 13px; // 添加行高确保文字垂直居中
        }
    }

    .product-card {
        background: #fff;
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 10px;
        margin-left: 0;
        width: 345px;
        height: 125px;
        .supplier-info {
            padding-bottom: 10px;
            border-bottom: 1px solid #f5f5f5;
            display: flex;
            align-items: center;
            gap: 5px;
            color: #333;
            font-size: 14px;
        }

        .product-info {
            display: flex;
            padding-top: 10px;
            gap: 10px;

            .product-img {
                width: 80px;
                height: 80px;
                object-fit: cover;
                border-radius: 4px;
            }

            .info-right {
                flex: 1;

                .product-name {
                    width: 209px;
                    height: 28px;
                    font-family: PingFang SC;
                    font-weight: 500;
                    font-size: 13px;
                    color: #333333;
                    margin-left: 8px;
                }

                .product-spec {
                    font-size: 12px;
                    color: #999;
                    margin-bottom: 10px;
                }

                .price-stepper {
                    .dec {
                        width: auto;
                        height: 11px;
                        font-family: PingFang SC;
                        font-weight: 400;
                        font-size: 12px;
                        color: #999999;
                        line-height: 5px;
                    }
                    position: absolute;
                    bottom: -12px;
                    display: flex;
                    justify-content: flex-end; // 修改为 flex-end
                    align-items: center;
                    width: 100%;
                    :deep(.van-stepper) {
                        margin-left: auto; // 使用 margin-left: auto 将步进器推到最右边
                    }
                }
            }
        }
    }

    .price-detail {
        background: #fff;
        border-radius: 8px;
        padding: 12px;

        .price-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            color: #333;

            .amount {
                color: #fa541c;
                font-weight: 500;
            }
        }
    }

    .bottom-action {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: 68px;
        background: #fff;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 12px;
        padding-bottom: 20px;
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);

        .total-price {
            font-size: 14px;
            margin-left: 27px;
            .price {
                font-family: PingFang SC;
                font-size: 24px;
                font-weight: 600;
                color: #fa541c;
                line-height: 24px;
                &::before {
                    content: "¥";
                    font-size: 14px;
                    font-weight: 600;
                    margin-right: 2px;
                }
                .decimal {
                    font-size: 14px;
                    font-weight: 600;
                }
            }
        }

        .submit-btn {
            width: 150px;
            height: 39px;
            background: linear-gradient(90deg, #ff7430 0%, #ff9d2e 100%);
            border-radius: 19px;
            border: none; // 移除边框
            margin-right: 15px; // 添加右边距
        }
    }
}
</style>
