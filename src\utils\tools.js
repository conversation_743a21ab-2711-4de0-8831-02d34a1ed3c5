/*
 * @Author: shenpp
 * @Date: 2023-05-25
 * @LastEditTime: 2024-02-28
 * @Description: 获取token的方法
 */

const tools = {
    // 生成随机数
    generateRandomString(length) {
        let result = "";
        const characters =
            "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

        for (let i = 0; i < length; i++) {
            result += characters.charAt(
                Math.floor(Math.random() * characters.length)
            );
        }
        return result;
    },

    // 从小程序得到的用户信息无userId及token，在跳转预约挂号时需要请求接口得到userId及token
    mergeUser(userInfo, noToken) {
        userInfo = { ...userInfo };
        userInfo.paperId = "01";
        userInfo.paperType = "1";
        userInfo.paperName = "居民身份证";
        userInfo.verifyRealName = "true";
        userInfo.name = userInfo.name;
        userInfo.userName = userInfo.name;
        userInfo.maskName = userInfo.name;
        userInfo.paperNo = userInfo.paperNum || userInfo.paperNumber;
        userInfo.paperNum = userInfo.paperNum || userInfo.paperNumber;
        userInfo.maskCertNo = userInfo.paperNum || userInfo.paperNumber;
        userInfo.phone = userInfo.phone || userInfo.phoneNo;
        userInfo.phoneNo = userInfo.phone || userInfo.phoneNo;
        userInfo.maskMobile = userInfo.phone || userInfo.phoneNo;
        userInfo.userId =
            userInfo.userId || window.localStorage.getItem("userId") || "";
        if (noToken && !window.sessionStorage.getItem("token")) {
            // localStorage中取的用户信息  不能插入token  有可能已经失效了 
            userInfo.token = "";
        } else {
            userInfo.token = userInfo.token || window.sessionStorage.getItem("token") || "";
            if (userInfo.token || window.sessionStorage.getItem("token")) {
                window.sessionStorage.setItem(
                    "token",
                    userInfo.token || window.sessionStorage.getItem("token")
                );
                window.localStorage.setItem(
                    "token",
                    userInfo.token || window.sessionStorage.getItem("token")
                );
            }
        }

        // window.sessionStorage.getItem("token")  local中的token有可能失效
        // debugger;
        // 存储使用者信息
        /* if (userInfo.token || window.sessionStorage.getItem("token")) {
          window.sessionStorage.setItem(
            "token",
            userInfo.token || window.sessionStorage.getItem("token")
          );
          window.localStorage.setItem(
            "token",
            userInfo.token || window.sessionStorage.getItem("token")
          );
        } */
        /** 因与会话中的userid冲突，删除
                                                            if (userInfo.userId || window.sessionStorage.getItem("userId")) {
                                                              window.sessionStorage.setItem(
                                                                "userId",
                                                                userInfo.userId || window.sessionStorage.getItem("userId")
                                                              );
                                                              window.localStorage.setItem(
                                                                "userId",
                                                                userInfo.userId || window.sessionStorage.getItem("userId")
                                                              );
                                                            }*/
        if (userInfo.userId) {
            window.localStorage.setItem(
                "userId",
                userInfo.userId || window.sessionStorage.getItem("userId")
            );
        }
        window.sessionStorage.setItem("userInfo", JSON.stringify(userInfo));
        window.localStorage.setItem("userInfo", JSON.stringify(userInfo));
        window.sessionStorage.setItem("myInfo", JSON.stringify(userInfo));
        window.localStorage.setItem("myInfo", JSON.stringify(userInfo));
        return userInfo;
    },
};

const handleImageUrl = (url) => {
    if (!url) return ''
    if (url.startsWith('http')) return url
    return `https://cdnweb11.96225.com/${url}`
  }

export { tools as default, handleImageUrl };


export function checkToken() {
    if (
        window.localStorage.getItem("userId") &&
        window.localStorage.getItem("token") &&
        window.localStorage.getItem("userInfo")
    ) {
        const userInfo = JSON.parse(window.localStorage.getItem("userInfo"))
        window.localStorage.setItem("userInfo", JSON.stringify(tools.mergeUser(userInfo)))
        window.localStorage.setItem("wxAppid", userInfo.wxAppid|| userInfo.appId);
        window.localStorage.setItem("wxOpenid", userInfo.wxOpenid|| userInfo.thirdId);
        return true;
    }
    return false;
}


  